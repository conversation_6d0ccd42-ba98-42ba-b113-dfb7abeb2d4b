package cn.taqu.mp.dto;

import cn.taqu.mp.annotation.SearchIgnore;
import cn.taqu.mp.util.AvatarTool;
import cn.taqu.soa.utils.JsonUtils;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;
import java.util.Collections;
import java.util.List;

import static cn.taqu.mp.constant.UserInfoConstant.*;

/**
 * <AUTHOR>
 * @date 2025/1/21 下午2:51
 */
@Data
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class UserCardInfoDto implements Serializable, MappingKey, Categorized {

    public static final UserCardInfoDto UNKNOWN = new UserCardInfoDto();

    @SearchIgnore
    private String uuid;

    private String defaultCardId;

    private String avatar = DEFAULT_AVATAR;

    private String accountName = DEFAULT_ACCOUNT_NAME;

    private String sexType = DEFAULT_SEX_TYPE;

    private Integer enableLocation;

    /**
     * 不需要返回
     */
    private Long birth;

    @SearchIgnore
    private String age = DEFAULT_AGE;

    private String accountStatus = DEFAULT_ACCOUNT_STATUS;

    private String passIntroductionContent = StringUtils.EMPTY;

    /**
     * 身份证号加密处理
     */
    private String identityNoDesc = StringUtils.EMPTY;

    private List<IntroductionImg> passIntroductionImgs = Collections.emptyList();

    private String voiceSign;

    private String voiceSignPreUrl;

    /**
     * 语音签名审核状态
     */
    private String passVoiceSign;

    /**
     * 语音签名
     */
    private String passVoiceSignUrl;

    /**
     * 时长
     */
    private String passVoiceSignDuration;

    /**
     * 只有数值，类似175，没带单位
     */
    private String height = StringUtils.EMPTY;

    private String weight = StringUtils.EMPTY;

    private String hometown = StringUtils.EMPTY;

    private String affectivestatus = StringUtils.EMPTY;

    private String profession = StringUtils.EMPTY;

    private String education = StringUtils.EMPTY;

    private String income = StringUtils.EMPTY;

    private String passPersonalProfile = StringUtils.EMPTY;

    /**
     * 星座
     */
    private String constellation;

    private String chatDressId;

    private String avatarDressId;

    private String city;

    private String baseaddr;

    private String baseaddrCity;

    /**
     * 相同分类(非卡片场景下不处理)
     */
    private List<String> sameCategory = Collections.emptyList();

    /**
     * 他的标签(非卡片场景下不处理)
     */
    private List<Label> labelList = Collections.emptyList();

    public void setPassIntroductionImgs(String json) {
        this.passIntroductionImgs = JsonUtils.stringToObject(json, new TypeReference<>() {});
    }

    @Data
    @JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
    public static class IntroductionImg {
        private String imgName;
        private Integer width;
        private Integer height;

        public void setImgName(String imgName) {
            this.imgName = AvatarTool.resolve(imgName);
        }
    }

    @Data
    @JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
    public static class Label {
        private String labelName;
        private Integer isHighlight;
    }

}
