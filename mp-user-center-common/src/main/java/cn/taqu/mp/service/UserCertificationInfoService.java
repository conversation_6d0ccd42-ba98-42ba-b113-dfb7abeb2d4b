package cn.taqu.mp.service;

import cn.taqu.mp.component.FastInfoSearcher;
import cn.taqu.mp.component.MappingKeyScanner;
import cn.taqu.mp.constant.EnableStatus;
import cn.taqu.mp.constant.LiveFaceDetectStatus;
import cn.taqu.mp.constant.RedisKeyConstant;
import cn.taqu.mp.constant.UserInfoField;
import cn.taqu.mp.dao.AccountsCertificationDao;
import cn.taqu.mp.dao.AccountsPhotoDao;
import cn.taqu.mp.dto.PhotoInfoDto;
import cn.taqu.mp.dto.UserCertificationInfoDto;
import cn.taqu.mp.entity.AccountsCertification;
import cn.taqu.mp.entity.AccountsPhoto;
import cn.taqu.mp.soa.EncryptClient;
import cn.taqu.mp.vo.UserCertificationInfoVo;
import cn.taqu.soa.utils.JsonUtils;
import com.beust.jcommander.internal.Lists;
import jakarta.annotation.Resource;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RBatch;
import org.redisson.api.RBucketAsync;
import org.redisson.api.RMapAsync;
import org.redisson.api.RedissonClient;
import org.redisson.client.codec.StringCodec;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.stream.Collectors;

import static cn.taqu.mp.constant.UserInfoConstant.*;

/**
 * <AUTHOR>
 * @date 2025/5/19 上午11:39
 */
@Service
@RequiredArgsConstructor
public class UserCertificationInfoService implements FastInfoSearcher<UserCertificationInfoDto> {

    private static final String                      IDENTITY_NO_18 = "****************";

    private static final String                      IDENTITY_NO_15 = "*************";

    private final static String                      PASS_CERT_TEXT = "本人真实照片，已通过人脸比对";

    private final RedissonClient                  redissonBizClient;

    private final AccountsCertificationDao accountsCertificationDao;

    private final EncryptClient                       encryptClient;

    private final RedissonClient                     redissonClient;

    private final AccountsPhotoDao                         photoDao;

    @Resource
    private UserInfoService                             infoService;

    @Override
    public Map<String, UserCertificationInfoVo> search(String myUuid, List<String> uuids) {
        var infoMap = infoService.info(uuids, UserCertificationInfoDto.class, () -> UserCertificationInfoDto.UNKNOWN);
        realAvatarProcess(infoMap,
            UserCertificationInfoDto::getRealPersonCertification,
            dto -> dto.setRealAvatarCertification("1"),
            dto -> dto.setRealAvatarCertification("0")
        );
        identityNoMaskProcess(infoMap);
        Map<String, UserCertificationInfoVo> result = new HashMap<>();
        infoMap.forEach((uuid, cert) -> {
            UserCertificationInfoVo vo = new UserCertificationInfoVo(JsonUtils.mapper().convertValue(cert, UserCertificationInfoVo.Certification.class));
            certProcess(vo.certification());
            result.put(uuid, vo);
        });
        return result;
    }

    @Override
    public Set<String> filterFields(Collection<String> fields) {
        var availableFields = MappingKeyScanner.me().fields(UserCertificationInfoDto.class);
        Set<String> targetFields = new HashSet<>(fields);
        // 移除不符合范围的字段
        targetFields.removeIf(r -> !availableFields.contains(r));
        if (targetFields.contains(UserInfoField.REAL_AVATAR_CERTIFICATION)) {
            targetFields.add(UserInfoField.REAL_PERSON_CERTIFICATION);
        }
        if (targetFields.contains(UserInfoField.GENDER_CERTIFICATION)) {
            targetFields.add(UserInfoField.ZHIMA_CERTIFICATION);
        }
        return targetFields;
    }

    /**
     *
     * @param uuids
     * @param fields
     * @return
     */
    @Override
    public Map<String, Map<String, String>> search(List<String> uuids, Collection<String> fields) {
        var targetFields = filterFields(fields);
        if (CollectionUtils.isEmpty(targetFields)) {
            return new HashMap<>(2);
        }
        Map<String, Map<String, String>> result = infoService.query(uuids, targetFields);
        result.forEach((uuid, info) -> each(info, targetFields));
        if (targetFields.contains(UserInfoField.REAL_AVATAR_CERTIFICATION)) {
            realAvatarProcess(result,
                cert -> cert.get(UserInfoField.REAL_PERSON_CERTIFICATION),
                cert -> cert.put(UserInfoField.REAL_AVATAR_CERTIFICATION, "1"),
                cert -> cert.put(UserInfoField.REAL_AVATAR_CERTIFICATION, "0")
            );
        }
        return result;
    }

    @Override
    public void postSearch(Map<String, Map<String, String>> infoMap, Set<String> filteredFields) {
        infoMap.forEach((uuid, info) -> each(info, filteredFields));
        if (filteredFields.contains(UserInfoField.REAL_AVATAR_CERTIFICATION)) {
            realAvatarProcess(infoMap,
                cert -> cert.get(UserInfoField.REAL_PERSON_CERTIFICATION),
                cert -> cert.put(UserInfoField.REAL_AVATAR_CERTIFICATION, "1"),
                cert -> cert.put(UserInfoField.REAL_AVATAR_CERTIFICATION, "0")
            );
        }
    }

    private void each(Map<String, String> info, Set<String> filteredFields) {
        if (filteredFields.contains(UserInfoField.ZHIMA_CERTIFICATION)) {
            String val = info.get(UserInfoField.ZHIMA_CERTIFICATION);
            if (StringUtils.isEmpty(val)) info.put(UserInfoField.ZHIMA_CERTIFICATION, DEFAULT_ZHI_MA_CERTIFICATION);
        }

        if (filteredFields.contains(UserInfoField.GENDER_CERTIFICATION)) {
            // 性别认证，直接用实名认证替换
            String val = info.getOrDefault(UserInfoField.ZHIMA_CERTIFICATION, DEFAULT_ZHI_MA_CERTIFICATION);
            info.put(UserInfoField.GENDER_CERTIFICATION, val);
        }

        if (filteredFields.contains(UserInfoField.FACE_CERTIFICATION)) {
            String val = info.get(UserInfoField.FACE_CERTIFICATION);
            if (StringUtils.isEmpty(val)) info.put(UserInfoField.FACE_CERTIFICATION, DEFAULT_FACE_CERTIFICATION);
        }

        if (filteredFields.contains(UserInfoField.REAL_PERSON_CERTIFICATION)) {
            String val = info.get(UserInfoField.REAL_PERSON_CERTIFICATION);
            if (StringUtils.isEmpty(val)) info.put(UserInfoField.REAL_PERSON_CERTIFICATION, DEFAULT_REAL_PERSON_CERTIFICATION);
        }

        if (filteredFields.contains(UserInfoField.REAL_PERSON_CERTIFICATION_STATUS)) {
            String val = info.get(UserInfoField.REAL_PERSON_CERTIFICATION_STATUS);
            if (StringUtils.isEmpty(val)) info.put(UserInfoField.REAL_PERSON_CERTIFICATION_STATUS, DEFAULT_REAL_PERSON_CERTIFICATION);
        }

        if (filteredFields.contains(UserInfoField.REGISTER_AVATAR_STATUS)) {
            String val = info.get(UserInfoField.REGISTER_AVATAR_STATUS);
            if (StringUtils.isEmpty(val)) info.put(UserInfoField.REGISTER_AVATAR_STATUS, "0");
        }

        if (filteredFields.contains(UserInfoField.PRE_AVATAR_FACE_STATUS)) {
            String val = info.get(UserInfoField.PRE_AVATAR_FACE_STATUS);
            if (StringUtils.isEmpty(val)) info.put(UserInfoField.PRE_AVATAR_FACE_STATUS, "0");
        }
        if (filteredFields.contains(UserInfoField.VOICE_CERTIFICATION)) {
            String val = info.get(UserInfoField.VOICE_CERTIFICATION);
            if (StringUtils.isEmpty(val)) info.put(UserInfoField.VOICE_CERTIFICATION, DEFAULT_VOICE_CERTIFICATION);
        }
    }

    private <T> void realAvatarProcess(Map<String, T> map, Function<T, String> realPersonGetter, Consumer<T> realAvatarSetter, Consumer<T> realAvatarDefault) {
        if (!map.isEmpty()) {
            RBatch pipeline = redissonClient.createBatch();
            Set<String> cacheMissed = new HashSet<>();
            map.forEach((uuid, cert) -> {
                Optional.ofNullable(realAvatarDefault).ifPresent(d -> d.accept(cert));
                if (EnableStatus.ENABLE.getStatus().toString().equals(realPersonGetter.apply(cert))) {
                    String key = RedisKeyConstant.HIS_PHOTO.formatted(uuid);
                    RMapAsync<String, String> cache = pipeline.getMap(key, StringCodec.INSTANCE);
                    cache.getAsync("1").thenAccept(p -> {
                        if (StringUtils.isEmpty(p)) {
                            cacheMissed.add(uuid);
                        } else {
                            PhotoInfoDto avatar = JsonUtils.stringToObject(p, PhotoInfoDto.class);
                            if (AccountsPhoto.Status.PASS.getValue() == avatar.getStatus() && LiveFaceDetectStatus.AUTHORIZED.getStatus() == avatar.getVerifyStatus()) {
                                realAvatarSetter.accept(cert);
                            }
                        }
                    });
                }
            });
            pipeline.execute();
            if (CollectionUtils.isNotEmpty(cacheMissed)) {
                List<PhotoInfoDto> list = photoDao.listRealPersonAvatar(cacheMissed);
                for (PhotoInfoDto avatar : list) {
                    T t = map.get(avatar.getAccountUuid());
                    realAvatarSetter.accept(t);
                }
            }

        }
    }

    /**
     * 身份号加密处理
     * @param result
     */
    private void identityNoMaskProcess(Map<String, UserCertificationInfoDto> result) {
        Collection<String> uuidList = result.entrySet().stream().filter(target ->
                Objects.equals(EnableStatus.ENABLE.getStatus().toString(), target.getValue().getZhimaCertification())
            ).map(Map.Entry::getKey).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(uuidList)) {
            return;
        }

        // 先查缓存吧
        List<String> needQueryDb = getIdentityNoMaskCache(result, uuidList);
        if (CollectionUtils.isEmpty(needQueryDb)) {
            return;
        }

        // db处理
        getIdentityNoFromDbAndSetCache(result, needQueryDb);
    }

    /**
     * 身份证加密处理
     *
     * @param result
     * @param needQueryDb
     */
    private void getIdentityNoFromDbAndSetCache(Map<String, UserCertificationInfoDto> result, Collection<String> needQueryDb) {
        // 数据身份证密文
        List<AccountsCertification> certifications = accountsCertificationDao.findByAccountUuidIn(needQueryDb);
        Map<String, String> cipherMap = certifications.stream().collect(Collectors.toMap(AccountsCertification::getIdentityNoCipher, AccountsCertification::getAccountUuid, (k1, k2) -> k1));
        Map<String, String> encryptContent = cipherMap.keySet().stream().collect(Collectors.toMap(item -> item, item -> item));
        // 身份解密
        Map<String, String> decryptMap = encryptClient.batchDecrypt(encryptContent);
        if (decryptMap.isEmpty()) {
            return;
        }

        RBatch pipeline = redissonBizClient.createBatch();
        StringCodec codec = StringCodec.INSTANCE;
        decryptMap.forEach((cipher, identityNo) -> {
            if (StringUtils.isBlank(identityNo)) {
                return;
            }

            String uuid = cipherMap.get(cipher);
            UserCertificationInfoDto certInfo = result.get(uuid);
            // 打码
            String mask = identityMask(identityNo);
            certInfo.setIdentityNoDesc(mask);
            // 入缓存
            String key = RedisKeyConstant.IDENTITY_NO_MASK.formatted(uuid);
            RBucketAsync<String> bucket = pipeline.getBucket(key, codec);
            bucket.setAsync(mask, 7, TimeUnit.DAYS);
        });
        pipeline.execute();
    }


    /**
     * 获取身份证打码号
     *
     * @param result
     * @param uuidList
     * @return
     */
    private List<String> getIdentityNoMaskCache(Map<String, UserCertificationInfoDto> result, Collection<String> uuidList) {
        List<String> needQueryDb = Lists.newArrayList();
        RBatch pipeline = redissonBizClient.createBatch();
        StringCodec codec = StringCodec.INSTANCE;
        uuidList.forEach(uuid -> {
            String key = RedisKeyConstant.IDENTITY_NO_MASK.formatted(uuid);
            RBucketAsync<String> bucket = pipeline.getBucket(key, codec);
            bucket.getAsync().thenAccept(idCache -> {
                if (StringUtils.isBlank(idCache)) {
                    needQueryDb.add(uuid);
                } else {
                    UserCertificationInfoDto certInfo = result.get(uuid);
                    certInfo.setIdentityNoDesc(idCache);
                }
            });
        });
        pipeline.execute();
        return needQueryDb;
    }

    /**
     * 身份认证处理
     *
     * @param target
     */
    private void certProcess(UserCertificationInfoVo.Certification target) {
        // 证件文案
        String enabled = EnableStatus.ENABLE.getStatus().toString();
        if (enabled.equals(target.getFaceCertification()) && enabled.equals(target.getRealAvatarCertification())) {
            target.setFaceCertificationDesc(PASS_CERT_TEXT);
        }
    }

    private String identityMask(String identityNo) {
        String start = identityNo.substring(0, 1);
        int length = identityNo.length();
        String end = identityNo.substring(length - 1, length);
        StringBuilder sb = new StringBuilder(start);
        if (identityNo.length() == 15) {
            sb.append(IDENTITY_NO_15);
        } else if (identityNo.length() == 18) {
            sb.append(IDENTITY_NO_18);
        } else {
            return "";
        }
        sb.append(end);
        String identityNoStr = sb.toString();
        if (identityNoStr.length() == 15 || identityNoStr.length() == 18) {
            return identityNoStr;
        }
        return "";
    }
}
