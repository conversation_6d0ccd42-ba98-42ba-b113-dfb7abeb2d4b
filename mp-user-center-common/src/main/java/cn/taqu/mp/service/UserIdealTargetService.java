package cn.taqu.mp.service;

import cn.taqu.mp.component.CategorizedInfoSearcher;
import cn.taqu.mp.constant.RedisKeyConstant;
import cn.taqu.mp.dao.AccountsIdealTargetDao;
import cn.taqu.mp.dto.UserIdealTargetDto;
import cn.taqu.mp.vo.UserIdealTargetVo;
import cn.taqu.soa.utils.JsonUtils;
import com.fasterxml.jackson.core.type.TypeReference;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections.CollectionUtils;
import org.redisson.api.RBatch;
import org.redisson.api.RBucketAsync;
import org.redisson.api.RedissonClient;
import org.redisson.client.codec.Codec;
import org.redisson.codec.TypedJsonJacksonCodec;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * 理想型
 * <AUTHOR>
 * @date 2025/4/28 下午4:14
 */
@Service
@RequiredArgsConstructor
public class UserIdealTargetService implements CategorizedInfoSearcher<UserIdealTargetDto> {

    private final Codec                           codec = new TypedJsonJacksonCodec(UserIdealTargetDto.class);

    private final RedissonClient      redissonBizClient;

    private final AccountsIdealTargetDao idealTargetDao;

    @Override
    public Map<String, UserIdealTargetVo> search(String myUuid, List<String> uuids) {
        Map<String, UserIdealTargetVo> result = new HashMap<>(uuids.size());
        Set<String> cacheMissed = new HashSet<>();
        RBatch pipeline = redissonBizClient.createBatch();
        for (String uuid : uuids) {
            String key = RedisKeyConstant.IDEAL_TARGET.formatted(uuid);
            RBucketAsync<UserIdealTargetDto> bucket = pipeline.getBucket(key, codec);
            bucket.getAsync().thenAccept(t -> {
                if (t != null) {
                    result.put(uuid, new UserIdealTargetVo(t.idealTarget()));
                } else {
                    cacheMissed.add(uuid);
                }
            });
        }
        pipeline.execute();
        // 查db
        if (CollectionUtils.isNotEmpty(cacheMissed)) {
            var list = idealTargetDao.findByAccountUuidIn(cacheMissed);
            for (var target : list) {
                result.put(target.getAccountUuid(), new UserIdealTargetVo(target.getIdealTarget()));
            }
        }
        return result;
    }

    @Override
    public Map<String, Map<String, String>> search(List<String> uuids, Collection<String> fields) {
        if (fields.contains("ideal_target")) {
            var map = search("", uuids);
            return JsonUtils.mapper().convertValue(map, new TypeReference<>() {});
        }
        return new HashMap<>(2);
    }
}
