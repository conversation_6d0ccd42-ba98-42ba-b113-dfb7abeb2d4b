package cn.taqu.mp.vo;

import cn.taqu.mp.dto.UserCardInfoDto;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/4/29 下午6:15
 */
@Data
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class UserCardInfoVo implements Serializable {

    private String uuid;

    private String avatar;

    private String accountName;

    private String sexType;

    private String age;

    private String accountStatus;

    private Introduction introduction;

    /**
     * 语音
     */
    private VoiceSignInfo voiceSignInfo;

    private String height;

    private String weight;

    private String hometown;

    private String affectivestatus;

    private String profession;

    private String education;

    private String income;

    // 标签相关模块 非卡片场景下不处理
    private Label labels;

    private String personalProfile;

    /**
     * 语音签名
     */
    @JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
    public record VoiceSignInfo(String voiceSignUrl, String voiceSignDuration) {

    }

    /**
     * 标签
     */
    @JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
    public record Label(List<String> sameCategory, List<UserCardInfoDto.Label> list) {

    }


    @JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
    public record Introduction(String passIntroductionContent, List<UserCardInfoDto.IntroductionImg> passIntroductionImgs) {

    }

}
