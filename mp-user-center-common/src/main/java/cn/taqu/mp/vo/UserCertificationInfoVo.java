package cn.taqu.mp.vo;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;

import java.io.Serializable;

import static cn.taqu.mp.constant.UserInfoConstant.DEFAULT_PROFILE_VERIFY_STATUS;

/**
 * <AUTHOR>
 * @date 2025/5/19 上午11:47
 */

public record UserCertificationInfoVo(Certification certification) implements Serializable {

    @Data
    @JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
    public static class Certification {

        private String identityNoDesc;

        private String faceCertification;

        private String realPersonCertification;

        private String zhimaCertification;

        private String realAvatarCertification;

        private String faceCertificationDesc;

    }

}
